<view class="container">
  <!-- 1. 订单基本信息组件（详情） -->
  <order-basic-info
    orderDetail="{{orderDetail}}"
    bind:reschedule="reschedule"
    bind:editAddress="editServiceAddress"
    bind:openNavigation="openNavigation"
  />

  <!-- 2. 追加服务模块 -->
  <module-container
    wx:if="{{orderDetail.status === '服务中' || orderDetail.status === '已完成' || orderDetail.status === '已评价' || orderDetail.status === '已取消' || orderDetail.status === '已退款'}}"
    title="追加服务"
    hasContent="{{pendingAdditionalServices.length > 0 || confirmedAdditionalServiceOrders.length > 0}}"
    loading="{{loadingStates.additionalServices}}"
    emptyText="暂无追加服务"
    moduleType="additional-services"
  >
    <!-- 待确认追加服务组件 -->
    <pending-additional-services
      pendingAdditionalServices="{{pendingAdditionalServices}}"
      bind:confirmService="confirmAdditionalService"
      bind:rejectService="rejectAdditionalService"
    />

    <!-- 追加服务记录组件 -->
    <additional-service-records
      confirmedAdditionalServiceOrders="{{confirmedAdditionalServiceOrders}}"
      bind:confirmService="confirmAdditionalService"
      bind:rejectService="rejectAdditionalService"
    />
  </module-container>

  <!-- 3. 服务时长统计模块 -->
  <module-container
    wx:if="{{orderDetail.status === '服务中' || orderDetail.status === '已完成' || orderDetail.status === '已评价' || orderDetail.status === '已取消' || orderDetail.status === '已退款'}}"
    title="服务时长"
    hasContent="{{orderDetail.status === '服务中' || serviceDurationRecords.length > 0}}"
    loading="{{loadingStates.serviceDurationRecords}}"
    emptyText="暂无服务时长记录"
    moduleType="service-duration"
  >
    <service-duration
      orderDetail="{{orderDetail}}"
      serviceDurationRecords="{{serviceDurationRecords}}"
      allAdditionalServices="{{allAdditionalServices}}"
      serviceDurationStatistics="{{serviceDurationStatistics}}"
      showServiceDuration="{{showServiceDuration}}"
      bind:toggleServiceDuration="toggleServiceDuration"
      bind:startMainService="startMainService"
      bind:startAdditionalService="startAdditionalService"
      bind:endServiceDuration="endServiceDuration"
    />
  </module-container>

  <!-- 4. 特殊情况说明模块 -->
  <module-container
    wx:if="{{orderDetail.status === '服务中' || orderDetail.status === '已完成' || orderDetail.status === '已评价' || orderDetail.status === '已取消' || orderDetail.status === '已退款'}}"
    title="特殊情况说明"
    hasContent="{{specialNoteData !== null}}"
    loading="{{false}}"
    emptyText="暂无特殊情况说明"
    showAction="{{orderDetail.status === '服务中'}}"
    actionText="添加说明"
    moduleType="special-note"
    bind:action="showSpecialNote"
  >
    <special-note-display
      orderDetail="{{orderDetail}}"
      specialNoteData="{{specialNoteData}}"
      bind:showSpecialNote="showSpecialNote"
    />
  </module-container>

  <!-- 5. 服务前后照片模块 -->
  <module-container
    wx:if="{{orderDetail.status === '服务中' || orderDetail.status === '已完成' || orderDetail.status === '已评价'}}"
    title="服务前后照片"
    hasContent="{{hasServicePhotos}}"
    loading="{{servicePhotosLoading}}"
    emptyText="暂无服务照片"
    moduleType="service-photos"
  >
    <service-photos-display
      servicePhotos="{{servicePhotos}}"
      hasServicePhotos="{{hasServicePhotos}}"
      loading="{{servicePhotosLoading}}"
    />
  </module-container>

  <!-- 6. 服务评价模块 -->
  <module-container
    wx:if="{{orderDetail.status === '已完成' || orderDetail.status === '已评价'}}"
    title="服务评价"
    hasContent="{{hasReview && reviewData && !reviewLoading}}"
    loading="{{reviewLoading}}"
    emptyText="客户暂未评价"
    moduleType="service-review"
  >
    <service-review
      orderDetail="{{orderDetail}}"
      reviewData="{{reviewData}}"
      hasReview="{{hasReview}}"
      reviewLoading="{{reviewLoading}}"
      bind:viewReview="viewReview"
    />
  </module-container>

  <!-- 更多操作弹窗 -->
  <view wx:if="{{showMoreActions}}" class="more-actions-dropdown">
    <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.orderId}}">
      更改服务地址
    </view>
    <view class="dropdown-item" bindtap="deleteOrder" data-order-id="{{item.orderId}}">
      更换服务人员
    </view>
    <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.orderId}}">
      取消订单
    </view>
  </view>
  <!-- 底部操作按钮组件 -->
  <bottom-actions
    orderDetail="{{orderDetail}}"
    bind:contactCustomer="contactCustomer"
    bind:viewReview="viewReview"
  />

  <!-- 时间选择器 -->
  <custom-picker
    wx:if="{{showTimePicker}}"
    bind:confirm="onTimeSelected"
    bind:cancel="onTimeCancel"
    selectedTime="{{selectedTime}}"
    hasBottomNavigation="{{false}}"
  />

  <!-- 拒绝原因输入模态框 -->
  <view class="reject-modal" wx:if="{{showRejectModal}}">
    <view class="modal-mask" bindtap="cancelReject"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">拒绝原因</text>
      </view>
      <view class="modal-body">
        <textarea
          class="reject-textarea"
          placeholder="请输入拒绝原因..."
          value="{{rejectReason}}"
          bindinput="onRejectReasonInput"
          maxlength="200"
        ></textarea>
        <view class="char-count">{{rejectReason.length}}/200</view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="cancelReject">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmReject">确认拒绝</button>
      </view>
    </view>
  </view>

  <!-- 特殊情况说明组件 -->
  <special-note-upload
    wx:if="{{showSpecialNote}}"
    show="{{showSpecialNote}}"
    orderInfo="{{orderDetail}}"
    readonly="{{specialNoteReadonly}}"
    noteData="{{specialNoteData}}"
    bind:confirm="onSpecialNoteConfirm"
    bind:cancel="onSpecialNoteCancel"
    bind:delete="onSpecialNoteDelete"
  />

  <!-- 地址编辑器组件 -->
  <address-editor
    wx:if="{{showAddressEditor}}"
    show="{{showAddressEditor}}"
    orderInfo="{{orderDetail}}"
    bind:confirm="onAddressEditConfirm"
    bind:cancel="onAddressEditCancel"
  />
</view>